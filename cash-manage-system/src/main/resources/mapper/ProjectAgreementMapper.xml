<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.modules.project.mapper.ProjectAgreementMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jinghang.cash.modules.project.domain.ProjectAgreement">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="template_code" property="templateCode" jdbcType="VARCHAR"/>
        <result column="contract_template_type" property="contractTemplateType" jdbcType="VARCHAR"/>
        <result column="contract_template_desc" property="contractTemplateDesc" jdbcType="VARCHAR"/>
        <result column="flow_loan_stage" property="flowLoanStage" jdbcType="VARCHAR"/>
        <result column="capital_loan_stage" property="capitalLoanStage" jdbcType="VARCHAR"/>
        <result column="template_owner" property="templateOwner" jdbcType="VARCHAR"/>
        <result column="template_owner_name" property="templateOwnerName" jdbcType="VARCHAR"/>
        <result column="capital_contract_name" property="capitalContractName" jdbcType="VARCHAR"/>
        <result column="flow_contract_name" property="flowContractName" jdbcType="VARCHAR"/>
        <result column="is_rd_signature" property="isRdSignature" jdbcType="VARCHAR"/>
        <result column="seal_type" property="sealType" jdbcType="VARCHAR"/>
        <result column="template_no" property="templateNo" jdbcType="VARCHAR"/>
        <result column="is_return_to_capital" property="isReturnToCapital" jdbcType="VARCHAR"/>
        <result column="is_return_to_flow" property="isReturnToFlow" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="enabled" property="enabled" jdbcType="VARCHAR"/>
        <result column="revision" property="revision" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, project_code, template_code, contract_template_type, contract_template_desc,
        flow_loan_stage, capital_loan_stage, template_owner, template_owner_name,
        capital_contract_name, flow_contract_name, is_rd_signature, seal_type,
        template_no, is_return_to_capital, is_return_to_flow, remark, enabled,
        revision, created_by, created_time, updated_by, updated_time
    </sql>

    <!-- 根据项目编码查询协议模板配置列表 -->
    <select id="selectByProjectCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_agreement
        WHERE project_code = #{projectCode,jdbcType=VARCHAR}
        AND enabled = 'ENABLE'
        ORDER BY created_time DESC
    </select>

    <!-- 根据项目编码和模板编码查询协议模板配置 -->
    <select id="selectByProjectCodeAndTemplateCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_agreement
        WHERE project_code = #{projectCode,jdbcType=VARCHAR}
        AND template_code = #{templateCode,jdbcType=VARCHAR}
        AND enabled = 'ENABLE'
        LIMIT 1
    </select>

    <!-- 查询所有启用状态的协议模板配置 -->
    <select id="selectAllEnabledAgreements" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_agreement
        WHERE enabled = 'ENABLE'
        ORDER BY project_code, created_time DESC
    </select>

</mapper>
