package com.jinghang.cash.modules.project.service;

import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.modules.project.domain.ProjectAgreement;

import java.util.List;

/**
 * 协议模板配置服务接口
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 09:45
 */
public interface ProjectAgreementService {

    /**
     * 根据项目代码、流程贷款阶段和合同模板类型获取项目协议
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @param contractTemplateType 合同模板类型
     * @return 项目协议
     */
    ProjectAgreement getProjectAgreementByProjectCodeAndStageAndTemplateType(String projectCode, String flowLoanStage, String capitalLoanStage, String contractTemplateType);

    /**
     * 根据项目代码和是否退回流程获取项目协议列表
     *
     * @param projectCode 项目编码
     * @param isReturnToFlow 是否回传流量方
     * @param isReturnToCapital 是否回传资金方
     * @return 项目协议列表
     */
    List<ProjectAgreement> getProjectAgreementsByProjectCodeAndReturnStatus(String projectCode, ActiveInactive isReturnToFlow, ActiveInactive isReturnToCapital);

    /**
     * 根据项目代码和流程贷款阶段获取项目协议列表
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @return 项目协议列表
     */
    List<ProjectAgreement> getProjectAgreementsByProjectCodeAndStage(String projectCode, String flowLoanStage, String capitalLoanStage);
}
