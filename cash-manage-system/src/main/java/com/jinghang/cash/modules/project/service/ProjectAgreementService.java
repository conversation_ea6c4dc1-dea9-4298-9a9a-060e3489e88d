package com.jinghang.cash.modules.project.service;

import com.jinghang.cash.api.dto.ProjectAgreementDto;

import java.util.List;

/**
 * 协议模板配置服务接口
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 09:45
 */
public interface ProjectAgreementService {

    /**
     * 根据项目编码查询协议模板配置列表
     *
     * @param projectCode 项目编码
     * @return 协议模板配置列表
     */
    List<ProjectAgreementDto> queryByProjectCode(String projectCode);

    /**
     * 根据项目编码和模板编码查询协议模板配置
     *
     * @param projectCode 项目编码
     * @param templateCode 模板编码
     * @return 协议模板配置
     */
    ProjectAgreementDto queryByProjectCodeAndTemplateCode(String projectCode, String templateCode);

    /**
     * 查询所有启用状态的协议模板配置
     *
     * @return 所有启用状态的协议模板配置列表
     */
    List<ProjectAgreementDto> queryAllEnabledAgreements();
}
