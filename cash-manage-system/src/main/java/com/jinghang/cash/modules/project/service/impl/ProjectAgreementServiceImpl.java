package com.jinghang.cash.modules.project.service.impl;

import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.modules.project.domain.ProjectAgreement;
import com.jinghang.cash.modules.project.mapper.ProjectAgreementMapper;
import com.jinghang.cash.modules.project.service.ProjectAgreementService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 协议模板配置服务实现类
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 09:50
 */
@Service
public class ProjectAgreementServiceImpl implements ProjectAgreementService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectAgreementServiceImpl.class);

    @Autowired
    private ProjectAgreementMapper projectAgreementMapper;

    /**
     * 根据项目编码查询协议模板配置列表
     *
     * @param projectCode 项目编码
     * @return 协议模板配置列表
     */
    @Override
    public List<ProjectAgreementDto> queryByProjectCode(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.info("项目编码为空，无法查询协议模板配置");
            return new ArrayList<>();
        }

        try {
            List<ProjectAgreement> projectAgreements = projectAgreementMapper.selectByProjectCode(projectCode);
            if (projectAgreements == null || projectAgreements.isEmpty()) {
                return new ArrayList<>();
            }

            List<ProjectAgreementDto> result = new ArrayList<>();
            for (ProjectAgreement projectAgreement : projectAgreements) {
                ProjectAgreementDto dto = new ProjectAgreementDto();
                BeanUtils.copyProperties(projectAgreement, dto);
                result.add(dto);
            }

            return result;
        } catch (Exception e) {
            logger.error("根据项目编码查询协议模板配置异常，projectCode: {}", projectCode, e);
            throw new RuntimeException("查询协议模板配置失败", e);
        }
    }

    /**
     * 根据项目编码和模板编码查询协议模板配置
     *
     * @param projectCode 项目编码
     * @param templateCode 模板编码
     * @return 协议模板配置
     */
    @Override
    public ProjectAgreementDto queryByProjectCodeAndTemplateCode(String projectCode, String templateCode) {
        if (StringUtils.isBlank(projectCode) || StringUtils.isBlank(templateCode)) {
            logger.info("项目编码或模板编码为空，无法查询协议模板配置");
            return null;
        }

        try {
            ProjectAgreement projectAgreement = projectAgreementMapper.selectByProjectCodeAndTemplateCode(projectCode, templateCode);
            if (projectAgreement == null) {
                return null;
            }

            ProjectAgreementDto dto = new ProjectAgreementDto();
            BeanUtils.copyProperties(projectAgreement, dto);
            return dto;
        } catch (Exception e) {
            logger.error("根据项目编码和模板编码查询协议模板配置异常，projectCode: {}, templateCode: {}", projectCode, templateCode, e);
            throw new RuntimeException("查询协议模板配置失败", e);
        }
    }

    /**
     * 查询所有启用状态的协议模板配置
     *
     * @return 所有启用状态的协议模板配置列表
     */
    @Override
    public List<ProjectAgreementDto> queryAllEnabledAgreements() {
        try {
            List<ProjectAgreement> projectAgreements = projectAgreementMapper.selectAllEnabledAgreements();
            if (projectAgreements == null || projectAgreements.isEmpty()) {
                return new ArrayList<>();
            }

            List<ProjectAgreementDto> result = new ArrayList<>();
            for (ProjectAgreement projectAgreement : projectAgreements) {
                ProjectAgreementDto dto = new ProjectAgreementDto();
                BeanUtils.copyProperties(projectAgreement, dto);
                result.add(dto);
            }

            return result;
        } catch (Exception e) {
            logger.error("查询所有启用状态的协议模板配置异常", e);
            throw new RuntimeException("查询所有启用状态的协议模板配置失败", e);
        }
    }
}
