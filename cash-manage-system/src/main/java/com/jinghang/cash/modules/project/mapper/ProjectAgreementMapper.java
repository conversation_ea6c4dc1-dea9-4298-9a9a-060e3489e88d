package com.jinghang.cash.modules.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.modules.project.domain.ProjectAgreement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 协议模板配置Mapper
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 09:40
 */
@Mapper
public interface ProjectAgreementMapper extends BaseMapper<ProjectAgreement> {

    /**
     * 根据项目编码查询协议模板配置列表
     *
     * @param projectCode 项目编码
     * @return 协议模板配置列表
     */
    List<ProjectAgreement> selectByProjectCode(@Param("projectCode") String projectCode);

    /**
     * 根据项目编码和模板编码查询协议模板配置
     *
     * @param projectCode 项目编码
     * @param templateCode 模板编码
     * @return 协议模板配置
     */
    ProjectAgreement selectByProjectCodeAndTemplateCode(@Param("projectCode") String projectCode, 
                                                       @Param("templateCode") String templateCode);

    /**
     * 查询所有启用状态的协议模板配置
     *
     * @return 启用状态的协议模板配置列表
     */
    List<ProjectAgreement> selectAllEnabledAgreements();
}
