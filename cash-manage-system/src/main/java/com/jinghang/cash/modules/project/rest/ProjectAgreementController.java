package com.jinghang.cash.modules.project.rest;

import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.modules.project.domain.ProjectAgreement;
import com.jinghang.cash.modules.project.service.ProjectAgreementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 协议模板配置控制器
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 10:10
 */
@RestController
@RequestMapping("/api/project/agreement")
@Tag(name = "协议模板配置管理", description = "协议模板配置相关接口")
public class ProjectAgreementController {

    @Autowired
    private ProjectAgreementService projectAgreementService;

    /**
     * 根据项目代码、流程贷款阶段和合同模板类型获取项目协议
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @param contractTemplateType 合同模板类型
     * @return 项目协议
     */
    @GetMapping("/queryByStageAndType")
    @Operation(summary = "根据项目代码、阶段和模板类型获取项目协议", description = "根据项目代码、流程贷款阶段和合同模板类型获取项目协议")
    public ProjectAgreement getProjectAgreementByProjectCodeAndStageAndTemplateType(
            @Parameter(description = "项目编码", required = true)
            @RequestParam("projectCode") String projectCode,
            @Parameter(description = "资产方合同签署阶段")
            @RequestParam(value = "flowLoanStage", required = false) String flowLoanStage,
            @Parameter(description = "资金方合同签署阶段")
            @RequestParam(value = "capitalLoanStage", required = false) String capitalLoanStage,
            @Parameter(description = "合同模板类型", required = true)
            @RequestParam("contractTemplateType") String contractTemplateType) {
        return projectAgreementService.getProjectAgreementByProjectCodeAndStageAndTemplateType(projectCode, flowLoanStage, capitalLoanStage, contractTemplateType);
    }

    /**
     * 根据项目代码和是否退回流程获取项目协议列表
     *
     * @param projectCode 项目编码
     * @param isReturnToFlow 是否回传流量方
     * @param isReturnToCapital 是否回传资金方
     * @return 项目协议列表
     */
    @GetMapping("/queryByReturnStatus")
    @Operation(summary = "根据项目代码和退回状态获取项目协议列表", description = "根据项目代码和是否退回流程获取项目协议列表")
    public List<ProjectAgreement> getProjectAgreementsByProjectCodeAndReturnStatus(
            @Parameter(description = "项目编码", required = true)
            @RequestParam("projectCode") String projectCode,
            @Parameter(description = "是否回传流量方")
            @RequestParam(value = "isReturnToFlow", required = false) ActiveInactive isReturnToFlow,
            @Parameter(description = "是否回传资金方")
            @RequestParam(value = "isReturnToCapital", required = false) ActiveInactive isReturnToCapital) {
        return projectAgreementService.getProjectAgreementsByProjectCodeAndReturnStatus(projectCode, isReturnToFlow, isReturnToCapital);
    }

    /**
     * 根据项目代码和流程贷款阶段获取项目协议列表
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @return 项目协议列表
     */
    @GetMapping("/queryByStage")
    @Operation(summary = "根据项目代码和阶段获取项目协议列表", description = "根据项目代码和流程贷款阶段获取项目协议列表")
    public List<ProjectAgreement> getProjectAgreementsByProjectCodeAndStage(
            @Parameter(description = "项目编码", required = true)
            @RequestParam("projectCode") String projectCode,
            @Parameter(description = "资产方合同签署阶段")
            @RequestParam(value = "flowLoanStage", required = false) String flowLoanStage,
            @Parameter(description = "资金方合同签署阶段")
            @RequestParam(value = "capitalLoanStage", required = false) String capitalLoanStage) {
        return projectAgreementService.getProjectAgreementsByProjectCodeAndStage(projectCode, flowLoanStage, capitalLoanStage);
    }
}
